#!/usr/bin/env python3
"""
Simple test script to verify code generation API endpoints work.
This script tests the basic functionality without requiring external dependencies.
"""

import sys
import os
sys.path.append('.')

from fastapi.testclient import TestClient
from app.main import app

def test_api_endpoints():
    """Test that the code generation API endpoints are accessible."""
    client = TestClient(app)
    
    print("Testing Code Generation API Endpoints...")
    
    # Test 1: Health check (should work without auth)
    try:
        response = client.get("/")
        print(f"✓ Root endpoint: {response.status_code}")
    except Exception as e:
        print(f"✗ Root endpoint failed: {e}")
    
    # Test 2: Code generation endpoint (should require auth)
    try:
        response = client.post("/api/v1/code-generation/generate", json={
            "requirement_id": 1,
            "project_id": 1,
            "automation_framework": "selenium",
            "programming_language": "python"
        })
        # Should return 401 (unauthorized) since we're not authenticated
        if response.status_code == 401:
            print("✓ Code generation endpoint requires authentication")
        else:
            print(f"? Code generation endpoint returned: {response.status_code}")
    except Exception as e:
        print(f"✗ Code generation endpoint failed: {e}")
    
    # Test 3: Session status endpoint (should require auth)
    try:
        response = client.get("/api/v1/code-generation/session/test-session-id")
        if response.status_code == 401:
            print("✓ Session status endpoint requires authentication")
        else:
            print(f"? Session status endpoint returned: {response.status_code}")
    except Exception as e:
        print(f"✗ Session status endpoint failed: {e}")
    
    # Test 4: User sessions endpoint (should require auth)
    try:
        response = client.get("/api/v1/code-generation/sessions/user")
        if response.status_code == 401:
            print("✓ User sessions endpoint requires authentication")
        else:
            print(f"? User sessions endpoint returned: {response.status_code}")
    except Exception as e:
        print(f"✗ User sessions endpoint failed: {e}")
    
    print("API endpoint tests completed!")

def test_service_initialization():
    """Test that all services can be initialized."""
    print("\nTesting Service Initialization...")
    
    try:
        from app.services.git_service import GitRepositoryService
        import tempfile
        temp_dir = tempfile.mkdtemp()
        git_service = GitRepositoryService(temp_dir)
        print("✓ Git Repository Service initialized")
    except Exception as e:
        print(f"✗ Git Repository Service failed: {e}")
    
    try:
        from app.services.code_generation_service import CodeGenerationService
        code_gen_service = CodeGenerationService()
        print("✓ Code Generation Service initialized")
    except Exception as e:
        print(f"✗ Code Generation Service failed: {e}")
    
    try:
        from app.services.file_management_service import FileManagementService
        file_service = FileManagementService()
        print("✓ File Management Service initialized")
    except Exception as e:
        print(f"✗ File Management Service failed: {e}")
    
    try:
        from app.services.code_embedding_service import CodeEmbeddingService
        # This might fail if Qdrant is not running, which is expected
        print("✓ Code Embedding Service import successful")
    except Exception as e:
        print(f"? Code Embedding Service import issue (expected if Qdrant not running): {e}")
    
    print("Service initialization tests completed!")

def test_model_imports():
    """Test that all models can be imported."""
    print("\nTesting Model Imports...")
    
    try:
        from app.models.code_generation import (
            CodeGenerationSession, GeneratedFile, CodeGenerationLog,
            ProjectCodeMetadata, CodeGenerationStatus, CodeGenerationType
        )
        print("✓ Code Generation Models imported")
    except Exception as e:
        print(f"✗ Code Generation Models failed: {e}")
    
    try:
        from app.schemas.code_generation import (
            CodeGenerationRequest, CodeGenerationProgress,
            CodeGenerationSummary, RepositoryAnalysis
        )
        print("✓ Code Generation Schemas imported")
    except Exception as e:
        print(f"✗ Code Generation Schemas failed: {e}")
    
    try:
        from app.crud.code_generation import (
            create_code_generation_session, get_code_generation_session,
            update_code_generation_session
        )
        print("✓ Code Generation CRUD imported")
    except Exception as e:
        print(f"✗ Code Generation CRUD failed: {e}")
    
    print("Model import tests completed!")

def test_basic_functionality():
    """Test basic functionality of services."""
    print("\nTesting Basic Functionality...")
    
    # Test Git Service basic operations
    try:
        from app.services.git_service import GitRepositoryService
        import tempfile
        import shutil
        from pathlib import Path
        
        temp_dir = Path(tempfile.mkdtemp())
        git_service = GitRepositoryService(str(temp_dir))
        
        # Test path methods
        project_path = git_service.get_project_path("test_project")
        repo_path = git_service.get_repo_path("test_project")
        
        # Test file operations
        success = git_service.write_file_content("test_project", "test.py", "print('hello')")
        if success:
            content = git_service.read_file_content("test_project", "test.py")
            if content == "print('hello')":
                print("✓ Git Service basic file operations work")
            else:
                print("✗ Git Service file read/write mismatch")
        else:
            print("✗ Git Service file write failed")
        
        # Cleanup
        shutil.rmtree(temp_dir)
        
    except Exception as e:
        print(f"✗ Git Service basic functionality failed: {e}")
    
    # Test Code Generation Service basic operations
    try:
        from app.services.code_generation_service import CodeGenerationService
        
        service = CodeGenerationService()
        
        # Test class name sanitization
        result = service._sanitize_class_name("User Login Test")
        if result == "UserLoginTest":
            print("✓ Code Generation Service class name sanitization works")
        else:
            print(f"✗ Code Generation Service sanitization failed: {result}")
        
        # Test element filtering
        page_elements = {
            "elements": [
                {
                    "tag_name": "input",
                    "type": "text",
                    "css_selector": "#username"
                }
            ]
        }
        filtered = service._filter_relevant_elements(page_elements)
        if len(filtered) > 0:
            print("✓ Code Generation Service element filtering works")
        else:
            print("✗ Code Generation Service element filtering failed")
        
    except Exception as e:
        print(f"✗ Code Generation Service basic functionality failed: {e}")
    
    # Test File Management Service basic operations
    try:
        from app.services.file_management_service import FileManagementService
        
        service = FileManagementService()
        
        # Test file validation
        valid_file = {
            "path": "test.py",
            "content": "print('test')",
            "type": "test_file"
        }
        result = service._validate_single_file(valid_file)
        if result["valid"]:
            print("✓ File Management Service validation works")
        else:
            print(f"✗ File Management Service validation failed: {result['errors']}")
        
        # Test skip file logic
        skip_result = service._should_skip_file(".DS_Store")
        keep_result = service._should_skip_file("main.py")
        if skip_result and not keep_result:
            print("✓ File Management Service skip logic works")
        else:
            print(f"✗ File Management Service skip logic failed: skip .DS_Store={skip_result}, keep main.py={not keep_result}")
        
    except Exception as e:
        print(f"✗ File Management Service basic functionality failed: {e}")
    
    print("Basic functionality tests completed!")

def main():
    """Run all tests."""
    print("=" * 60)
    print("Code Generation Feature Test Suite")
    print("=" * 60)
    
    test_model_imports()
    test_service_initialization()
    test_basic_functionality()
    test_api_endpoints()
    
    print("\n" + "=" * 60)
    print("Test Suite Completed!")
    print("=" * 60)
    print("\nNote: Some tests may show warnings if external services")
    print("(like Qdrant vector database) are not running. This is expected.")
    print("\nTo run the full application:")
    print("1. Start Qdrant: docker run -p 6333:6333 qdrant/qdrant")
    print("2. Start Ollama: ollama serve")
    print("3. Start the backend: uvicorn app.main:app --reload")

if __name__ == "__main__":
    main()
